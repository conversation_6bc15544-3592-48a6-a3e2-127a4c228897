<!DOCTYPE html>
<html lang="en">
<head>
    <title>Weather 3D Volume Cloud Demo</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
    <style>
        body {
            margin: 0;
            background-color: #000011;
            color: #fff;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            color: white;
            font-size: 14px;
        }

        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 250px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
            font-size: 12px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group h4 {
            margin: 0 0 8px 0;
            color: #ccc;
            font-size: 14px;
        }

        .control-item {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-item label {
            flex: 1;
            margin-right: 10px;
        }

        .control-item input[type="range"] {
            flex: 2;
            margin-right: 8px;
        }

        .control-item span {
            min-width: 40px;
            text-align: right;
            font-family: monospace;
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
        }

        button:hover {
            background: #45a049;
        }

        button.active {
            background: #ff6b6b;
        }

        select {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 4px;
            border-radius: 4px;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 200;
            text-align: center;
            font-size: 18px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div id="info">
        Weather 3D Volume Cloud Demo<br>
        <small>Loading 10 weather images...</small>
    </div>

    <div id="loading">
        <div>Loading Weather Data...</div>
        <div id="progress">0/10 images loaded</div>
    </div>

    <div id="controls" class="hidden">
        <div class="control-group">
            <h4>Animation</h4>
            <div class="control-item">
                <label>Opacity:</label>
                <input type="range" id="opacity" min="0" max="1" step="0.01" value="1">
                <span id="opacityValue">1.00</span>
            </div>
            <div class="control-item">
                <label>Speed:</label>
                <input type="range" id="speed" min="0" max="5" step="0.1" value="1">
                <span id="speedValue">1.0</span>
            </div>
            <div class="control-item">
                <label>Time:</label>
                <input type="range" id="time" min="0" max="9" step="0.1" value="0">
                <span id="timeValue">0.0</span>
            </div>
            <div class="control-item">
                <button id="playPause">Play</button>
                <button id="reset">Reset</button>
            </div>
        </div>

        <div class="control-group">
            <h4>Rendering</h4>
            <div class="control-item">
                <label>Threshold:</label>
                <input type="range" id="threshold" min="0" max="1" step="0.01" value="0.25">
                <span id="thresholdValue">0.25</span>
            </div>
            <div class="control-item">
                <label>Steps:</label>
                <input type="range" id="steps" min="32" max="128" step="1" value="64">
                <span id="stepsValue">64</span>
            </div>
            <div class="control-item">
                <label>Style:</label>
                <select id="renderStyle">
                    <option value="volume">Volume</option>
                    <option value="isosurface">Isosurface</option>
                </select>
            </div>
        </div>

        <div class="control-group">
            <h4>Cut-off</h4>
            <div class="control-item">
                <label>Min:</label>
                <input type="range" id="cutoffMin" min="0" max="1" step="0.01" value="0">
                <span id="cutoffMinValue">0.00</span>
            </div>
            <div class="control-item">
                <label>Max:</label>
                <input type="range" id="cutoffMax" min="0" max="1" step="0.01" value="1">
                <span id="cutoffMaxValue">1.00</span>
            </div>
        </div>

        <div class="control-group">
            <h4>Performance</h4>
            <div class="control-item">
                <label>Scale:</label>
                <input type="range" id="scale" min="0.5" max="2" step="0.1" value="1">
                <span id="scaleValue">1.0</span>
            </div>
        </div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "../build/three.module.js",
                "three/addons/": "./jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { GUI } from 'three/addons/libs/lil-gui.module.min.js';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        // Global variables
        let scene, camera, renderer, controls;
        let material, mesh, clock;
        let volumeTextures = [];
        let isLoaded = false;
        let isAnimating = false;
        let currentTime = 0;

        // Weather image filenames (exactly 10 images as specified)
        const imageFilenames = [
            '090000.jpg', '090600.jpg', '091200.jpg', '091800.jpg', '092400.jpg',
            '093000.jpg', '093600.jpg', '094200.jpg', '094800.jpg', '095400.jpg'
        ];

        // Configuration
        const config = {
            opacity: 1.0,
            speed: 1.0,
            time: 0.0,
            threshold: 0.25,
            steps: 64,
            renderStyle: 'volume',
            cutoffMin: 0.0,
            cutoffMax: 1.0,
            scale: 1.0
        };

        init();

        async function init() {
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000011);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 3);

            // Create renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            document.body.appendChild(renderer.domElement);

            // Create controls
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // Create clock
            clock = new THREE.Clock();

            // Add lights
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            scene.add(directionalLight);

            // Load weather data
            await loadWeatherData();

            // Setup UI controls
            setupControls();

            // Start animation
            animate();
        }

        async function loadWeatherData() {
            try {
                const loadPromises = imageFilenames.map((filename, index) =>
                    loadWeatherImage(filename, index)
                );

                volumeTextures = await Promise.all(loadPromises);

                // Create volume material and mesh
                createVolumeMesh();

                isLoaded = true;
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('controls').classList.remove('hidden');
                document.getElementById('info').innerHTML = 'Weather 3D Volume Cloud Demo<br><small>Use mouse to rotate, zoom</small>';

                console.log('Weather data loaded successfully');
            } catch (error) {
                console.error('Failed to load weather data:', error);
                document.getElementById('loading').innerHTML = '<div style="color: red;">Failed to load weather data</div>';
            }
        }

        async function loadWeatherImage(filename, index) {
            return new Promise((resolve, reject) => {
                const loader = new THREE.TextureLoader();
                loader.load(
                    `./data/${filename}`,
                    (texture) => {
                        // Update progress
                        document.getElementById('progress').textContent = `${index + 1}/10 images loaded`;

                        // Convert image to 3D texture data
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        const img = texture.image;

                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);

                        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                        const data = new Uint8Array(canvas.width * canvas.height);

                        // Convert RGBA to grayscale for volume data
                        for (let i = 0; i < imageData.data.length; i += 4) {
                            const gray = (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3;
                            data[i / 4] = gray;
                        }

                        resolve({
                            data: data,
                            width: canvas.width,
                            height: canvas.height
                        });
                    },
                    undefined,
                    (error) => {
                        console.error(`Failed to load ${filename}:`, error);
                        reject(error);
                    }
                );
            });
        }

        function createVolumeMesh() {
            if (volumeTextures.length === 0) return;

            // Get dimensions from first texture
            const { width, height } = volumeTextures[0];
            const depth = volumeTextures.length;

            // Create 3D volume data
            const volumeData = new Uint8Array(width * height * depth);

            for (let z = 0; z < depth; z++) {
                const textureData = volumeTextures[z].data;
                for (let i = 0; i < textureData.length; i++) {
                    volumeData[z * width * height + i] = textureData[i];
                }
            }

            // Create 3D texture
            const texture3D = new THREE.Data3DTexture(volumeData, width, height, depth);
            texture3D.format = THREE.RedFormat;
            texture3D.type = THREE.UnsignedByteType;
            texture3D.minFilter = THREE.LinearFilter;
            texture3D.magFilter = THREE.LinearFilter;
            texture3D.wrapS = THREE.ClampToEdgeWrapping;
            texture3D.wrapT = THREE.ClampToEdgeWrapping;
            texture3D.wrapR = THREE.ClampToEdgeWrapping;
            texture3D.needsUpdate = true;

            // Create volume material
            material = createVolumeMaterial(texture3D, width, height, depth);

            // Create geometry and mesh
            const geometry = new THREE.BoxGeometry(2, 2, 2);
            mesh = new THREE.Mesh(geometry, material);
            scene.add(mesh);
        }

        function createVolumeMaterial(texture3D, width, height, depth) {
            const vertexShader = `
                varying vec3 vOrigin;
                varying vec3 vDirection;

                void main() {
                    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
                    vOrigin = vec3(inverse(modelViewMatrix) * vec4(0.0, 0.0, 0.0, 1.0)).xyz;
                    vDirection = position - vOrigin;
                    gl_Position = projectionMatrix * mvPosition;
                }
            `;

            const fragmentShader = `
                precision highp float;
                precision highp sampler3D;

                uniform sampler3D u_data;
                uniform vec3 u_size;
                uniform vec2 u_clim;
                uniform int u_renderstyle;
                uniform float u_renderthreshold;
                uniform float u_opacity;
                uniform float u_time;
                uniform int u_steps;

                varying vec3 vOrigin;
                varying vec3 vDirection;

                vec2 hitBox(vec3 orig, vec3 dir) {
                    vec3 box_min = vec3(-0.5);
                    vec3 box_max = vec3(0.5);
                    vec3 inv_dir = 1.0 / dir;
                    vec3 tmin_tmp = (box_min - orig) * inv_dir;
                    vec3 tmax_tmp = (box_max - orig) * inv_dir;
                    vec3 tmin = min(tmin_tmp, tmax_tmp);
                    vec3 tmax = max(tmin_tmp, tmax_tmp);
                    float t0 = max(tmin.x, max(tmin.y, tmin.z));
                    float t1 = min(tmax.x, min(tmax.y, tmax.z));
                    return vec2(t0, t1);
                }

                float sampleVolume(vec3 texCoord, float timeOffset) {
                    vec3 coord = texCoord + 0.5;

                    // Interpolate between time slices
                    float timeIndex = timeOffset * (u_size.z - 1.0);
                    float z1 = floor(timeIndex) / u_size.z;
                    float z2 = ceil(timeIndex) / u_size.z;
                    float t = fract(timeIndex);

                    float sample1 = texture(u_data, vec3(coord.xy, z1)).r;
                    float sample2 = texture(u_data, vec3(coord.xy, z2)).r;

                    return mix(sample1, sample2, t);
                }

                vec4 applyColormap(float intensity) {
                    float normalizedIntensity = (intensity - u_clim.x) / (u_clim.y - u_clim.x);
                    normalizedIntensity = clamp(normalizedIntensity, 0.0, 1.0);

                    // Weather colormap: blue to red
                    vec3 color;
                    if (normalizedIntensity < 0.25) {
                        color = mix(vec3(0.0, 0.0, 0.5), vec3(0.0, 0.5, 1.0), normalizedIntensity * 4.0);
                    } else if (normalizedIntensity < 0.5) {
                        color = mix(vec3(0.0, 0.5, 1.0), vec3(0.0, 1.0, 0.0), (normalizedIntensity - 0.25) * 4.0);
                    } else if (normalizedIntensity < 0.75) {
                        color = mix(vec3(0.0, 1.0, 0.0), vec3(1.0, 1.0, 0.0), (normalizedIntensity - 0.5) * 4.0);
                    } else {
                        color = mix(vec3(1.0, 1.0, 0.0), vec3(1.0, 0.0, 0.0), (normalizedIntensity - 0.75) * 4.0);
                    }

                    return vec4(color, normalizedIntensity);
                }

                void main() {
                    vec3 rayDir = normalize(vDirection);
                    vec2 bounds = hitBox(vOrigin, rayDir);

                    if (bounds.x > bounds.y) discard;

                    bounds.x = max(bounds.x, 0.0);

                    vec3 p = vOrigin + bounds.x * rayDir;
                    float stepSize = (bounds.y - bounds.x) / float(u_steps);

                    vec4 color = vec4(0.0);

                    for (int i = 0; i < 256; i++) {
                        if (i >= u_steps) break;

                        vec3 pos = vOrigin + (bounds.x + float(i) * stepSize) * rayDir;
                        float intensity = sampleVolume(pos, u_time);

                        if (u_renderstyle == 0) {
                            // Volume rendering
                            if (intensity > u_clim.x && intensity < u_clim.y) {
                                vec4 src = applyColormap(intensity);
                                src.a *= u_opacity * stepSize * 100.0;
                                color.rgb = color.rgb + (1.0 - color.a) * src.a * src.rgb;
                                color.a = color.a + (1.0 - color.a) * src.a;

                                if (color.a >= 0.95) break;
                            }
                        } else {
                            // Isosurface rendering
                            if (intensity > u_renderthreshold) {
                                color = applyColormap(intensity);
                                color.a = u_opacity;
                                break;
                            }
                        }
                    }

                    gl_FragColor = color;
                }
            `;

            return new THREE.ShaderMaterial({
                uniforms: {
                    u_data: { value: texture3D },
                    u_size: { value: new THREE.Vector3(width, height, depth) },
                    u_clim: { value: new THREE.Vector2(config.cutoffMin, config.cutoffMax) },
                    u_renderstyle: { value: 0 }, // 0: volume, 1: isosurface
                    u_renderthreshold: { value: config.threshold },
                    u_opacity: { value: config.opacity },
                    u_time: { value: config.time },
                    u_steps: { value: config.steps }
                },
                vertexShader: vertexShader,
                fragmentShader: fragmentShader,
                side: THREE.BackSide,
                transparent: true
            });
        }

        function setupControls() {
            // Opacity control
            const opacitySlider = document.getElementById('opacity');
            const opacityValue = document.getElementById('opacityValue');
            opacitySlider.addEventListener('input', (e) => {
                config.opacity = parseFloat(e.target.value);
                opacityValue.textContent = config.opacity.toFixed(2);
                updateUniforms();
            });

            // Speed control
            const speedSlider = document.getElementById('speed');
            const speedValue = document.getElementById('speedValue');
            speedSlider.addEventListener('input', (e) => {
                config.speed = parseFloat(e.target.value);
                speedValue.textContent = config.speed.toFixed(1);
            });

            // Time control
            const timeSlider = document.getElementById('time');
            const timeValue = document.getElementById('timeValue');
            timeSlider.addEventListener('input', (e) => {
                config.time = parseFloat(e.target.value) / 9.0; // Normalize to 0-1
                timeValue.textContent = e.target.value;
                updateUniforms();
            });

            // Threshold control
            const thresholdSlider = document.getElementById('threshold');
            const thresholdValue = document.getElementById('thresholdValue');
            thresholdSlider.addEventListener('input', (e) => {
                config.threshold = parseFloat(e.target.value);
                thresholdValue.textContent = config.threshold.toFixed(2);
                updateUniforms();
            });

            // Steps control
            const stepsSlider = document.getElementById('steps');
            const stepsValue = document.getElementById('stepsValue');
            stepsSlider.addEventListener('input', (e) => {
                config.steps = parseInt(e.target.value);
                stepsValue.textContent = config.steps;
                updateUniforms();
            });

            // Render style control
            const renderStyleSelect = document.getElementById('renderStyle');
            renderStyleSelect.addEventListener('change', (e) => {
                config.renderStyle = e.target.value;
                updateUniforms();
            });

            // Cutoff controls
            const cutoffMinSlider = document.getElementById('cutoffMin');
            const cutoffMinValue = document.getElementById('cutoffMinValue');
            cutoffMinSlider.addEventListener('input', (e) => {
                config.cutoffMin = parseFloat(e.target.value);
                cutoffMinValue.textContent = config.cutoffMin.toFixed(2);
                updateUniforms();
            });

            const cutoffMaxSlider = document.getElementById('cutoffMax');
            const cutoffMaxValue = document.getElementById('cutoffMaxValue');
            cutoffMaxSlider.addEventListener('input', (e) => {
                config.cutoffMax = parseFloat(e.target.value);
                cutoffMaxValue.textContent = config.cutoffMax.toFixed(2);
                updateUniforms();
            });

            // Scale control
            const scaleSlider = document.getElementById('scale');
            const scaleValue = document.getElementById('scaleValue');
            scaleSlider.addEventListener('input', (e) => {
                config.scale = parseFloat(e.target.value);
                scaleValue.textContent = config.scale.toFixed(1);
                if (mesh) {
                    mesh.scale.setScalar(config.scale);
                }
            });

            // Play/Pause button
            const playPauseBtn = document.getElementById('playPause');
            playPauseBtn.addEventListener('click', () => {
                isAnimating = !isAnimating;
                playPauseBtn.textContent = isAnimating ? 'Pause' : 'Play';
                playPauseBtn.classList.toggle('active', isAnimating);
            });

            // Reset button
            const resetBtn = document.getElementById('reset');
            resetBtn.addEventListener('click', () => {
                config.time = 0;
                currentTime = 0;
                document.getElementById('time').value = 0;
                document.getElementById('timeValue').textContent = '0.0';
                updateUniforms();
            });
        }

        function updateUniforms() {
            if (!material) return;

            material.uniforms.u_clim.value.set(config.cutoffMin, config.cutoffMax);
            material.uniforms.u_renderstyle.value = config.renderStyle === 'volume' ? 0 : 1;
            material.uniforms.u_renderthreshold.value = config.threshold;
            material.uniforms.u_opacity.value = config.opacity;
            material.uniforms.u_time.value = config.time;
            material.uniforms.u_steps.value = config.steps;
        }

        function animate() {
            requestAnimationFrame(animate);

            if (isLoaded) {
                const deltaTime = clock.getDelta();

                // Update animation time
                if (isAnimating) {
                    currentTime += deltaTime * config.speed * 0.1;
                    if (currentTime > 1.0) currentTime = 0.0;

                    config.time = currentTime;
                    document.getElementById('time').value = (currentTime * 9).toFixed(1);
                    document.getElementById('timeValue').textContent = (currentTime * 9).toFixed(1);
                    updateUniforms();
                }

                // Update controls
                controls.update();

                // Render
                renderer.render(scene, camera);
            }
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>