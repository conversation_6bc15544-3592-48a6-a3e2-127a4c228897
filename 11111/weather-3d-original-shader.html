<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather 3D Volume Demo - Original Shader</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 100;
            min-width: 200px;
        }

        .control-group {
            margin-bottom: 10px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }

        input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .value-display {
            font-size: 11px;
            color: #ccc;
        }

        #info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="controls">
        <div class="control-group">
            <label for="timeSlider">Time: <span id="timeValue">0.0</span></label>
            <input type="range" id="timeSlider" min="0" max="1" step="0.01" value="0">
        </div>

        <div class="control-group">
            <label for="opacitySlider">Opacity: <span id="opacityValue">1.0</span></label>
            <input type="range" id="opacitySlider" min="0" max="1" step="0.01" value="1">
        </div>

        <div class="control-group">
            <label for="thresholdSlider">Threshold: <span id="thresholdValue">0.5</span></label>
            <input type="range" id="thresholdSlider" min="0" max="1" step="0.01" value="0.5">
        </div>

        <div class="control-group">
            <label for="cutoffMinSlider">Cutoff Min: <span id="cutoffMinValue">0.0</span></label>
            <input type="range" id="cutoffMinSlider" min="0" max="1" step="0.01" value="0">
        </div>

        <div class="control-group">
            <label for="cutoffMaxSlider">Cutoff Max: <span id="cutoffMaxValue">1.0</span></label>
            <input type="range" id="cutoffMaxSlider" min="0" max="1" step="0.01" value="1">
        </div>

        <div class="control-group">
            <button id="playPauseBtn">Play</button>
            <button id="resetBtn">Reset</button>
        </div>
    </div>

    <div id="info">
        <div>Weather 3D Volume Visualization</div>
        <div>Original Shader Implementation</div>
        <div>Mouse: Rotate | Wheel: Zoom</div>
    </div>

    <script type="module">
        import * as THREE from './three.module.js';
        import { OrbitControls } from './OrbitControls.js';

        let scene, camera, renderer, controls, clock;
        let mesh, material;
        let isPlaying = false;
        let animationId;

        const config = {
            time: 0.0,
            opacity: 1.0,
            threshold: 0.5,
            cutoffMin: 0.0,
            cutoffMax: 1.0,
            steps: 128
        };

        init();

        async function init() {
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000000);

            // Create camera (exactly like original)
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 3);

            // Create renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            document.body.appendChild(renderer.domElement);

            // Create controls (exactly like original)
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // Create clock
            clock = new THREE.Clock();

            // No floor - exactly like original

            // Load weather data
            await loadWeatherData();

            // Setup UI controls
            setupControls();

            // Start animation
            animate();
        }

        async function loadWeatherData() {
            console.log('Loading weather data...');

            const imageFiles = [];
            for (let i = 1; i <= 10; i++) {
                imageFiles.push(`data/weather_${i.toString().padStart(2, '0')}.jpg`);
            }

            const images = await Promise.all(
                imageFiles.map(file => {
                    return new Promise((resolve, reject) => {
                        const img = new Image();
                        img.crossOrigin = 'anonymous';
                        img.onload = () => resolve(img);
                        img.onerror = reject;
                        img.src = file;
                    });
                })
            );

            console.log('Images loaded, creating 3D texture...');

            // Get dimensions from first image
            const width = images[0].width;
            const height = images[0].height;
            const depth = images.length;

            // Create 3D texture data
            const data = new Uint8Array(width * height * depth);

            images.forEach((img, z) => {
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);

                const imageData = ctx.getImageData(0, 0, width, height);

                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const pixelIndex = (y * width + x) * 4;
                        const dataIndex = z * width * height + y * width + x;

                        // Use red channel as intensity
                        data[dataIndex] = imageData.data[pixelIndex];
                    }
                }
            });

            // Create 3D texture
            const texture3D = new THREE.Data3DTexture(data, width, height, depth);
            texture3D.format = THREE.RedFormat;
            texture3D.type = THREE.UnsignedByteType;
            texture3D.minFilter = THREE.LinearFilter;
            texture3D.magFilter = THREE.LinearFilter;
            texture3D.wrapS = THREE.ClampToEdgeWrapping;
            texture3D.wrapT = THREE.ClampToEdgeWrapping;
            texture3D.wrapR = THREE.ClampToEdgeWrapping;
            texture3D.needsUpdate = true;

            console.log('3D texture created successfully');

            // Create volume material
            material = createVolumeMaterial(texture3D, width, height, depth);

            // Create geometry and mesh (exactly like original)
            const geometry = new THREE.BoxGeometry(2, 2, 2);
            mesh = new THREE.Mesh(geometry, material);
            scene.add(mesh);
        }

        function createVolumeMaterial(texture3D, width, height, depth) {
            // Volume rendering shaders from original weather-demo-3d.js
            const vertexShader = `
                varying vec3 vOrigin;
                varying vec3 vDirection;

                void main() {
                    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
                    vOrigin = vec3(inverse(modelViewMatrix) * vec4(0.0, 0.0, 0.0, 1.0)).xyz;
                    vDirection = position - vOrigin;
                    gl_Position = projectionMatrix * mvPosition;
                }
            `;

            const fragmentShader = `
                precision highp float;
                precision highp sampler3D;

                uniform sampler3D u_data;
                uniform sampler2D u_colormap;
                uniform vec3 u_size;
                uniform vec2 u_clim;
                uniform int u_renderstyle;
                uniform float u_renderthreshold;
                uniform float u_opacity;
                uniform float u_time;

                varying vec3 vOrigin;
                varying vec3 vDirection;

                vec2 hitBox(vec3 orig, vec3 dir) {
                    vec3 box_min = vec3(-0.5);
                    vec3 box_max = vec3(0.5);
                    vec3 inv_dir = 1.0 / dir;
                    vec3 tmin_tmp = (box_min - orig) * inv_dir;
                    vec3 tmax_tmp = (box_max - orig) * inv_dir;
                    vec3 tmin = min(tmin_tmp, tmax_tmp);
                    vec3 tmax = max(tmin_tmp, tmax_tmp);
                    float t0 = max(tmin.x, max(tmin.y, tmin.z));
                    float t1 = min(tmax.x, min(tmax.y, tmax.z));
                    return vec2(t0, t1);
                }

                float sampleVolume(vec3 texCoord) {
                    return texture(u_data, texCoord + 0.5).r;
                }

                vec4 applyColormap(float intensity) {
                    float normalizedIntensity = (intensity - u_clim.x) / (u_clim.y - u_clim.x);
                    normalizedIntensity = clamp(normalizedIntensity, 0.0, 1.0);
                    return texture2D(u_colormap, vec2(normalizedIntensity, 0.5));
                }

                void main() {
                    vec3 rayDir = normalize(vDirection);
                    vec2 bounds = hitBox(vOrigin, rayDir);

                    if (bounds.x > bounds.y) discard;

                    bounds.x = max(bounds.x, 0.0);

                    vec3 p = vOrigin + bounds.x * rayDir;
                    vec3 inc = 1.0 / abs(rayDir);
                    float delta = min(inc.x, min(inc.y, inc.z)) / u_size.x;

                    vec4 color = vec4(0.0);

                    for (float t = bounds.x; t < bounds.y; t += delta) {
                        vec3 pos = vOrigin + t * rayDir;
                        float intensity = sampleVolume(pos);

                        if (u_renderstyle == 0) {
                            // Volume rendering
                            vec4 src = applyColormap(intensity);
                            src.a *= u_opacity * delta * 100.0;
                            color.rgb = color.rgb + (1.0 - color.a) * src.a * src.rgb;
                            color.a = color.a + (1.0 - color.a) * src.a;

                            if (color.a >= 0.95) break;
                        } else {
                            // Isosurface rendering
                            if (intensity > u_renderthreshold) {
                                color = applyColormap(intensity);
                                color.a = u_opacity;
                                break;
                            }
                        }
                    }

                    gl_FragColor = color;
                }
            `;

            return new THREE.ShaderMaterial({
                uniforms: {
                    u_data: { value: texture3D },
                    u_colormap: { value: createColorTexture() },
                    u_size: { value: new THREE.Vector3(width, height, depth) },
                    u_clim: { value: new THREE.Vector2(0.0, 1.0) },
                    u_renderstyle: { value: 0 }, // 0: volume, 1: isosurface
                    u_renderthreshold: { value: 0.5 },
                    u_opacity: { value: 1.0 },
                    u_time: { value: 0.0 }
                },
                vertexShader: vertexShader,
                fragmentShader: fragmentShader,
                side: THREE.BackSide,
                transparent: true
            });
        }

        function createColorTexture() {
            // Create a simple temperature colormap (from original file)
            const width = 256;
            const data = new Uint8Array(width * 4);

            for (let i = 0; i < width; i++) {
                const t = i / (width - 1);
                // Blue to red gradient for temperature
                data[i * 4] = Math.floor(255 * t); // R
                data[i * 4 + 1] = Math.floor(255 * (1 - Math.abs(t - 0.5) * 2)); // G
                data[i * 4 + 2] = Math.floor(255 * (1 - t)); // B
                data[i * 4 + 3] = 255; // A
            }

            const texture = new THREE.DataTexture(data, width, 1, THREE.RGBAFormat);
            texture.needsUpdate = true;
            return texture;
        }

        function setupControls() {
            const timeSlider = document.getElementById('timeSlider');
            const opacitySlider = document.getElementById('opacitySlider');
            const thresholdSlider = document.getElementById('thresholdSlider');
            const cutoffMinSlider = document.getElementById('cutoffMinSlider');
            const cutoffMaxSlider = document.getElementById('cutoffMaxSlider');
            const playPauseBtn = document.getElementById('playPauseBtn');
            const resetBtn = document.getElementById('resetBtn');

            timeSlider.addEventListener('input', (e) => {
                config.time = parseFloat(e.target.value);
                document.getElementById('timeValue').textContent = config.time.toFixed(2);
                if (material) {
                    material.uniforms.u_time.value = config.time;
                }
            });

            opacitySlider.addEventListener('input', (e) => {
                config.opacity = parseFloat(e.target.value);
                document.getElementById('opacityValue').textContent = config.opacity.toFixed(2);
                if (material) {
                    material.uniforms.u_opacity.value = config.opacity;
                }
            });

            thresholdSlider.addEventListener('input', (e) => {
                config.threshold = parseFloat(e.target.value);
                document.getElementById('thresholdValue').textContent = config.threshold.toFixed(2);
                if (material) {
                    material.uniforms.u_renderthreshold.value = config.threshold;
                }
            });

            cutoffMinSlider.addEventListener('input', (e) => {
                config.cutoffMin = parseFloat(e.target.value);
                document.getElementById('cutoffMinValue').textContent = config.cutoffMin.toFixed(2);
                if (material) {
                    material.uniforms.u_clim.value.x = config.cutoffMin;
                }
            });

            cutoffMaxSlider.addEventListener('input', (e) => {
                config.cutoffMax = parseFloat(e.target.value);
                document.getElementById('cutoffMaxValue').textContent = config.cutoffMax.toFixed(2);
                if (material) {
                    material.uniforms.u_clim.value.y = config.cutoffMax;
                }
            });

            playPauseBtn.addEventListener('click', () => {
                isPlaying = !isPlaying;
                playPauseBtn.textContent = isPlaying ? 'Pause' : 'Play';
            });

            resetBtn.addEventListener('click', () => {
                config.time = 0.0;
                timeSlider.value = config.time;
                document.getElementById('timeValue').textContent = config.time.toFixed(2);
                if (material) {
                    material.uniforms.u_time.value = config.time;
                }
                isPlaying = false;
                playPauseBtn.textContent = 'Play';
            });
        }

        function animate() {
            animationId = requestAnimationFrame(animate);

            if (isPlaying && material) {
                config.time += 0.005;
                if (config.time > 1.0) config.time = 0.0;

                document.getElementById('timeSlider').value = config.time;
                document.getElementById('timeValue').textContent = config.time.toFixed(2);
                material.uniforms.u_time.value = config.time;
            }

            controls.update();
            renderer.render(scene, camera);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        window.addEventListener('resize', onWindowResize);
    </script>
</body>
</html>
